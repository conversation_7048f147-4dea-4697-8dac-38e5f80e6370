---
title: Document
description: Generate lifelike speech from pdf, docx, pptx, and other document formats.
---

The GeminiGen.AI Document-to-Speech API allows you to convert files in various document formats into high-quality, natural-sounding speech. You can use this API to generate voiceovers for multimedia content, create narrations for e-books and documents, or turn subtitles into engaging audio experiences.

## Document To Speech
`POST https://api.geminigen.ai/v1/document-to-speech`

This endpoint allows you to convert document into speech. You can customize the voice, speed, and model used for the conversion.


### Example Request
::code-group
```bash [terminal]
curl -X POST https://api.geminigen.ai/v1/document-to-speech \
  -H "Content-Type: multipart/form-data" \
  -H "x-api-key: <your api key>" \
  --form "model=tts-1" \
  --form "voice_id=OA001" \
  --form "speed=1" \
  --form "file=@/path/to/your/document.pdf" \
  --form "file_password=your_password"
```

```ts [py]
import requests

url = "https://api.geminigen.ai/v1/document-to-speech"
headers = {
    "x-api-key": "<your api key>",
    "Content-Type": "multipart/form-data"
}
data = {
    "model": "tts-1",
    "voice_id": "OA001",
    "speed": 1,
    "file": open("/path/to/your/document.pdf", "rb"),
    "file_password": "your_password"
}

response = requests.post(url, headers=headers, files=data)
print(response.json())
```

```ts [ts]
import axios from 'axios';

const url = "https://api.geminigen.ai/v1/document-to-speech";
const headers = {
    "x-api-key": "<your api key>",
    "Content-Type": "multipart/form-data"
};
const data = new FormData();
data.append("model", "tts-1");
data.append("voice_id", "OA001");
data.append("speed", 1);
data.append("file", new File(["/path/to/your/document.pdf"], "document.pdf"));
data.append("file_password", "your_password");

axios.post(url, data, { headers }).then(response => {
    console.log(response.data);
});
```
::

### Request Attributes
<!-- model	string	chỉ có thể chọn tts-1 hoặc tts-1-hd, mặc định là tts-1	
voice_id	string	tham khảo danh sách voice id ở sheet bên cạnh, mặc định là OA001	
speed	float	từ 1-4, mặc định là 1	
input*	string	max 10000 chars	 -->
<!-- space -->

`model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model used for the conversion. You can choose between `tts-1` and `tts-1-hd`. The default value is `tts-1`.

`voice_id` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The voice used for the conversion. You can find the list of voice IDs in the [Voice Library](https://geminigen.ai/app/voice-library). The default value is `OA001`.

`speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech. The value should be between 1 and 4. The default value is 1.

`file` [string($binary)]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The document file to be converted. Supported formats include .docx , .xlsx , .pptx , .pdf , .epub , .mobi , .txt , .html , .odt , .ods , .odp , .azw , .azw3. The maximum file size is 100 MB and max 500,000 rows of data.

`file_password` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The password for the document file, if it is password-protected.

### Example Response
```json [Response]
{
  "success": true,
  "result": {
    "uuid": "4a7693ee-aa35-11ef-bfda-7eba07618aa0",
    "voice_id": "OA001",
    "speed": 1,
    "model": "tts-1",
    "tts_input": "5101447014.pdf",
    "estimated_credit": 0,
    "used_credit": 0,
    "status": 1,
    "status_percentage": 1,
    "error_message": "",
    "speaker_name": "Alloy",
    "created_at": "2024-11-24T07:25:33",
    "updated_at": "2024-11-24T07:25:33",
    "file_size": 98842
  }
}
```

### Response Attributes

`success` [boolean]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Indicates whether the request was successful.

`result` [object]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The result of the document-to-speech conversion.

`result.uuid` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The unique identifier for the conversion.

`result.voice_id` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The voice used for the conversion.

`result.speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech.

`result.model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model used for the conversion.

`result.tts_input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The document file that was converted into speech.

`result.estimated_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The estimated number of credits used for the conversion.

`result.used_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The actual number of credits used for the conversion.

`result.status` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The status of the conversion. Possible values are:

<!-- CONVERTING = 1
JOINING_AUDIO = 12
MERGING_AUDIO = 13
DOWNLOADING_AUDIO = 14
REWORKING = 11
COMPLETED = 2
ERROR = 3 -->

- `1`: Converting
- `2`: Completed
- `3`: Error
- `11`: Reworking
- `12`: Joining Audio
- `13`: Merging Audio
- `14`: Downloading Audio

`result.status_percentage` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The percentage of the conversion that has been completed.

`result.error_message` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The error message, if any.

`result.speaker_name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name of the speaker.

`result.created_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the conversion was created.

`result.updated_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The date and time when the conversion was last updated.

`result.file_size` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The size of the document file in bytes.
